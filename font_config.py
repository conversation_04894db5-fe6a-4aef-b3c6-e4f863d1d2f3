"""
字体配置模块
确保matplotlib能正确显示中文
"""

import matplotlib.pyplot as plt
import matplotlib as mpl
import platform
import warnings

def setup_font():
    """设置matplotlib的中文字体"""
    system = platform.system()
    
    # 设置支持中文的字体
    if system == 'Windows':
        # Windows系统
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
    elif system == 'Darwin':
        # macOS系统
        plt.rcParams['font.sans-serif'] = ['PingFang SC', 'Arial Unicode MS', 'DejaVu Sans']
    else:
        # Linux系统
        plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'DejaVu Sans']
    
    # 解决负号显示问题
    plt.rcParams['axes.unicode_minus'] = False
    
    # 设置图片DPI
    plt.rcParams['figure.dpi'] = 100
    plt.rcParams['savefig.dpi'] = 300
    
    # 设置默认字体大小
    plt.rcParams['font.size'] = 11
    plt.rcParams['axes.titlesize'] = 14
    plt.rcParams['axes.labelsize'] = 12
    plt.rcParams['xtick.labelsize'] = 10
    plt.rcParams['ytick.labelsize'] = 10
    plt.rcParams['legend.fontsize'] = 10
    
    # 设置图表样式
    plt.rcParams['axes.grid'] = True
    plt.rcParams['grid.alpha'] = 0.3
    plt.rcParams['axes.spines.top'] = False
    plt.rcParams['axes.spines.right'] = False
    
    # 忽略字体警告
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')

# 在导入时自动设置字体
setup_font() 