*HEADING
Concrete VUMAT Test - Uniaxial Cyclic Loading
** Units: MPa, mm, s
**
*NODE
1, 0.0, 0.0, 0.0
2, 100.0, 0.0, 0.0
3, 100.0, 100.0, 0.0
4, 0.0, 100.0, 0.0
5, 0.0, 0.0, 100.0
6, 100.0, 0.0, 100.0
7, 100.0, 100.0, 100.0
8, 0.0, 100.0, 100.0
**
*ELEMENT, TYPE=C3D8R
1, 1, 2, 3, 4, 5, 6, 7, 8
**
*SOLID SECTION, ELSET=ALL, MATERIAL=CONCRETE_DAMAGE
**
*MATERIAL, NAME=CONCRETE_DAMAGE
*USER MATERIAL, CONSTANTS=9
** E0,     f_t,     f_c,     A+,      B+,      xi+,     A-,      B-,      xi-
10000.0,  3.67043, 10.0,    0.84463, 1.81372, 0.5,     2.0,     1.32925, 0.50028
*DEPVAR
5
*DENSITY
2.4e-9
** 1: Plastic strain
** 2: Tensile damage d+
** 3: Compressive damage d-  
** 4: Maximum tensile damage force r_max+
** 5: Maximum compressive damage force r_max-
**
*INITIAL CONDITIONS, TYPE=SOLUTION
ALL, 0.0, 0.0, 0.0, 3.67043, 10.0
**
*ELSET, ELSET=ALL
1
**
*BOUNDARY
1, 1, 3
4, 1, 3
5, 1, 3
8, 1, 3
2, 2, 3
3, 2, 3
6, 2, 3
7, 2, 3
**
*AMPLITUDE, NAME=CYCLIC_LOAD
0.0, 0.0
0.1, 0.0002
0.2, 0.0
0.3, -0.0003
0.4, 0.0
0.5, 0.0004
0.6, 0.0
0.7, -0.0006
0.8, 0.0
0.9, 0.0006
1.0, 0.0
**
*STEP, NAME=CyclicLoading
*DYNAMIC, EXPLICIT
, 1.0
*BOUNDARY, AMPLITUDE=CYCLIC_LOAD
2, 1, 1, 100.0
3, 1, 1, 100.0
6, 1, 1, 100.0
7, 1, 1, 100.0
**
*OUTPUT, FIELD
*NODE OUTPUT
U, V, A
*ELEMENT OUTPUT
S, E, SDV
**
*OUTPUT, HISTORY, FREQUENCY=10
*ELEMENT OUTPUT, ELSET=ALL
S11, E11, SDV1, SDV2, SDV3, SDV4, SDV5
**
*END STEP 