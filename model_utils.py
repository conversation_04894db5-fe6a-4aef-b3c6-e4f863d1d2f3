"""
模型工具函数
处理PyTorch版本兼容性和模型加载问题
"""

import torch
import warnings


def safe_load_model(model_path, device='cpu'):
    """
    安全加载模型，兼容不同PyTorch版本
    
    Args:
        model_path: 模型文件路径
        device: 设备
    
    Returns:
        model_info: 加载的模型信息
    """
    print(f"正在加载模型: {model_path}")
    
    # 方法1: 尝试使用weights_only=False（PyTorch 2.6+推荐）
    try:
        model_info = torch.load(model_path, map_location=device, weights_only=False)
        print("使用安全模式加载成功")
        return model_info
    except Exception as e1:
        print(f"安全模式加载失败: {e1}")
        
        # 方法2: 尝试传统方式加载
        try:
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                model_info = torch.load(model_path, map_location=device)
            print("使用兼容模式加载成功")
            return model_info
        except Exception as e2:
            print(f"兼容模式加载也失败: {e2}")
            
            # 方法3: 尝试只加载state_dict
            try:
                print("尝试只加载模型权重...")
                checkpoint = torch.load(model_path, map_location=device, weights_only=True)
                if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                    print("检测到完整checkpoint格式，但只能加载权重")
                    return {'model_state_dict': checkpoint['model_state_dict']}
                else:
                    print("检测到纯权重格式")
                    return {'model_state_dict': checkpoint}
            except Exception as e3:
                print(f"权重加载也失败: {e3}")
                raise Exception(f"所有加载方法都失败了。原始错误: {e1}")


def safe_save_model(model_info, save_path):
    """
    安全保存模型，确保兼容性
    
    Args:
        model_info: 要保存的模型信息
        save_path: 保存路径
    """
    # 确保所有数值都是Python原生类型，避免numpy类型导致的序列化问题
    cleaned_info = {}
    
    for key, value in model_info.items():
        if key == 'model_state_dict':
            cleaned_info[key] = value
        elif isinstance(value, dict):
            cleaned_info[key] = {k: float(v) if hasattr(v, 'item') else v for k, v in value.items()}
        elif hasattr(value, 'item'):
            cleaned_info[key] = float(value.item())
        else:
            cleaned_info[key] = value
    
    torch.save(cleaned_info, save_path)


def check_pytorch_version():
    """检查PyTorch版本并给出建议"""
    version = torch.__version__
    print(f"当前PyTorch版本: {version}")
    
    major, minor = map(int, version.split('.')[:2])
    
    if major > 2 or (major == 2 and minor >= 6):
        print("检测到PyTorch 2.6+版本，已启用安全加载模式")
        print("如果遇到加载问题，建议:")
        print("1. 重新训练模型以生成兼容格式")
        print("2. 或使用 pip install torch==1.13.0 降级")
    else:
        print("使用较旧的PyTorch版本，加载应该没有问题")


# 测试函数
if __name__ == "__main__":
    check_pytorch_version() 