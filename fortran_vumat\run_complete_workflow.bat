@echo off
setlocal enabledelayedexpansion
rem 混凝土柱VUMAT完整验证工作流程

echo ===================================================
echo    混凝土柱VUMAT完整验证工作流程
echo ===================================================
echo.
echo 此脚本将引导您完成以下步骤：
echo 1. 运行Abaqus混凝土柱分析
echo 2. 等待分析完成
echo 3. 提取结果并与PINN预测对比
echo 4. 生成完整的分析报告和图表
echo.

pause

echo.
echo ===================================================
echo    步骤1: 运行Abaqus分析
echo ===================================================

call run_column_analysis.bat
if %ERRORLEVEL% NEQ 0 (
    echo ✗ Abaqus分析启动失败，工作流程终止
    pause
    exit /b 1
)

echo.
echo ===================================================
echo    步骤2: 等待分析完成
echo ===================================================
echo.
echo 正在等待Abaqus分析完成...
echo 这可能需要几分钟到几十分钟，取决于您的计算机性能。
echo.

rem 读取结果文件夹名称
if exist "latest_analysis_folder.txt" (
    set /p RESULTS_FOLDER=<latest_analysis_folder.txt
    echo 监控文件夹: !RESULTS_FOLDER!
) else (
    echo ✗ 无法找到分析结果文件夹记录
    pause
    exit /b 1
)

rem 等待ODB文件生成
:WAIT_LOOP
if exist "!RESULTS_FOLDER!\column_analysis.odb" (
    echo ✓ 分析完成，找到ODB文件
    goto ANALYSIS_COMPLETE
)

echo 分析仍在进行中，等待30秒后重新检查...
timeout /t 30 /nobreak >nul
goto WAIT_LOOP

:ANALYSIS_COMPLETE
echo.
echo ===================================================
echo    步骤3: 提取结果并生成对比图表
echo ===================================================

call run_column_extract.bat
if %ERRORLEVEL% NEQ 0 (
    echo ✗ 结果提取失败
    pause
    exit /b 1
)

echo.
echo ===================================================
echo    步骤4: 工作流程完成总结
echo ===================================================
echo.
echo ✓ 混凝土柱VUMAT验证工作流程已完成！
echo.
echo 生成的文件位于: !RESULTS_FOLDER!
echo.
echo 主要输出文件：
echo --------------------------------
if exist "!RESULTS_FOLDER!\column_results.csv" (
    echo ✓ column_results.csv - 原始数据
)
if exist "!RESULTS_FOLDER!\analysis_report.txt" (
    echo ✓ analysis_report.txt - 详细分析报告
)
if exist "!RESULTS_FOLDER!\plots" (
    echo ✓ plots/ - 图表目录
    echo   主要图表：
    if exist "!RESULTS_FOLDER!\plots\stress_strain_comparison.png" echo     ✓ stress_strain_comparison.png (滞回曲线对比)
    if exist "!RESULTS_FOLDER!\plots\displacement_time.png" echo     ✓ displacement_time.png (位移-时间曲线)
    if exist "!RESULTS_FOLDER!\plots\reaction_time.png" echo     ✓ reaction_time.png (反力-时间曲线)
    if exist "!RESULTS_FOLDER!\plots\reaction_displacement.png" echo     ✓ reaction_displacement.png (反力-位移曲线)
)
echo.
echo Abaqus原始文件：
echo --------------------------------
if exist "!RESULTS_FOLDER!\column_analysis.odb" echo ✓ column_analysis.odb - Abaqus结果数据库
if exist "!RESULTS_FOLDER!\column_analysis.dat" echo ✓ column_analysis.dat - 分析日志
if exist "!RESULTS_FOLDER!\column_analysis.sta" echo ✓ column_analysis.sta - 状态文件

echo.
echo ===================================================
echo    验证结果分析建议
echo ===================================================
echo.
echo 请查看以下文件来验证VUMAT实现：
echo.
echo 1. 打开 plots\stress_strain_comparison.png
echo    - 比较VUMAT模拟与PINN预测的滞回曲线
echo    - 检查两条曲线的形态是否一致
echo    - 关注滞回环的大小和形状
echo.
echo 2. 查看 analysis_report.txt
echo    - 了解详细的数值分析结果
echo    - 检查关键指标是否合理
echo.
echo 3. 如果需要进一步分析：
echo    - 在Abaqus/Viewer中打开 column_analysis.odb
echo    - 查看应力、应变和状态变量的分布
echo    - 制作动画观察损伤演化过程
echo.
echo ===================================================
echo    完成！
echo ===================================================
echo.
echo 混凝土柱VUMAT验证工作流程已成功完成。
echo 所有结果文件已保存在 !RESULTS_FOLDER! 文件夹中。
echo.
pause 