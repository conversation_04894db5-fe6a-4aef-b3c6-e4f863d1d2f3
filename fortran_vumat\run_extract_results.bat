@echo off
rem 在Abaqus Python环境中运行结果提取脚本

echo ===================================================
echo    混凝土VUMAT模型结果提取与分析
echo ===================================================

rem 检查Abaqus命令是否可用
where abaqus >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ 找到Abaqus命令
) else (
    echo ✗ 未找到Abaqus命令，请确保Abaqus已安装且在PATH中
    echo 尝试直接使用Python运行脚本...
    python extract_results.py
    goto :end
)

echo.
echo 步骤1: 在Abaqus Python环境中运行提取脚本
echo -----------------------------------
abaqus python extract_results.py
if %ERRORLEVEL% EQU 0 (
    echo ✓ 数据提取成功完成
) else (
    echo ✗ 数据提取失败，尝试使用普通Python运行...
    python extract_results.py
)

:end
echo.
echo ===================================================
echo    处理完成！
echo ===================================================
echo.
echo 生成的文件：
if exist "vumat_results.csv" echo   - vumat_results.csv (原始数据)
if exist "vumat_analysis_report.txt" echo   - vumat_analysis_report.txt (分析报告)
if exist "results_plots" echo   - results_plots/ (结果图表目录)
echo.
echo 完成！
pause 