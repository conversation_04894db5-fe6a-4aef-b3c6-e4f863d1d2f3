# --------------- Execution Environment -----------------
ABAQUSLM_LICENSE_FILE = 27800@while
ABAQUS_LANG = Chinese (Simplified)_China.936
ABAQUS_PY_TRANSLATION_DICTIONARY = Configuration/Xresources/zh_CN/zh_CN_PyDict.py
ABAQUS_SEQ = 2023_09_21-20.55.25 RELr426 190762
ABAQUS_TRANSLATION_DICTIONARY = Configuration/Xresources/zh_CN/zh_CN_Dict.py
ABA_BLA_LIBRARY_PATH = D:\ABAQUS2024\product;D:\ABAQUS2024\product\win_b64;D:\ABAQUS2024\product\win_b64\code;D:\ABAQUS2024\product\win_b64\code\bin;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal;D:\ABAQUS2024\product\win_b64\CAEresources;D:\ABAQUS2024\product\win_b64\SMA;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal\Interop;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal\Elysium;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10\lib;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10\DLLs
ABA_COMMAND = D:\ABAQUS2024\product\win_b64\code\bin\SMALauncher.exe
ABA_HOME = D:\ABAQUS2024\product\win_b64
ABA_LIBRARY_PATHNAME = PATH
ABA_MPI_LIBRARY_PATH = D:\ABAQUS2024\product;D:\ABAQUS2024\product\win_b64;D:\ABAQUS2024\product\win_b64\code;D:\ABAQUS2024\product\win_b64\code\bin;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal;D:\ABAQUS2024\product\win_b64\CAEresources;D:\ABAQUS2024\product\win_b64\SMA;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal\Interop;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal\Elysium;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10\lib;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10\DLLs
ABA_PATH = D:\ABAQUS2024\product;D:\ABAQUS2024\product\win_b64;D:\ABAQUS2024\product\win_b64\code;D:\ABAQUS2024\product\win_b64\code\bin;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal;D:\ABAQUS2024\product\win_b64\CAEresources;D:\ABAQUS2024\product\win_b64\SMA
ABA_PATH_BASE = D:\ABAQUS2024\product\win_b64;
ABQINFO = D:\ABAQUS2024\product\win_b64\SMA\info\
ABQLMHANGLIMIT = 0
ABQLMIMPL = FLEXNET
ABQLMQUEUE = 30
ABQLMUSER = kz
ABQ_DLALLOCATOR = 1
ABQ_SPLITFILE = 0
ALLUSERSPROFILE = C:\ProgramData
APPDATA = C:\Users\<USER>\AppData\Roaming
CATCOMMANDPATH = D:\ABAQUS2024\product\win_b64\code\command
CATDICTIONARYPATH = D:\ABAQUS2024\product\win_b64\code\dictionary
CLASSPATH = D:\oneapi\dal\latest\lib\onedal.jar;
CMAKE_PREFIX_PATH = D:\oneapi\compiler\latest\windows\IntelDPCPP;D:\oneapi\compiler\latest\windows\IntelDPCPP;D:\oneapi\compiler\latest\windows\IntelDPCPP;D:\oneapi\compiler\latest\windows\IntelDPCPP;D:\oneapi\tbb\latest\env\..;D:\oneapi\dal\latest;D:\oneapi\compiler\latest\windows\IntelDPCPP;
CMPLR_ROOT = D:\oneapi\compiler\latest
COMMANDPROMPTTYPE = Native
COMMONPROGRAMFILES = C:\Program Files\Common Files
COMMONPROGRAMFILES(X86) = C:\Program Files (x86)\Common Files
COMMONPROGRAMW6432 = C:\Program Files\Common Files
COMPUTERNAME = WHILE
COMSPEC = C:\Windows\system32\cmd.exe
CONDA_BAT = D:\Anaconda\condabin\conda.bat
CONDA_EXE = D:\Anaconda\Scripts\conda.exe
CONDA_SHLVL = 0
COVERAGE_RCFILE = D:\ABAQUS2024\product\win_b64\SMA\site\pyCoverageConfig.env
CPATH = D:\oneapi\compiler\latest\windows\include;D:\oneapi\compiler\latest\windows\compiler\include;D:\oneapi\compiler\latest\windows\compiler\include\intel64;D:\oneapi\compiler\latest\windows\include;D:\oneapi\compiler\latest\windows\compiler\include;D:\oneapi\compiler\latest\windows\compiler\include\intel64;D:\oneapi\compiler\latest\windows\include;D:\oneapi\compiler\latest\windows\compiler\include;D:\oneapi\compiler\latest\windows\compiler\include\intel64;D:\oneapi\compiler\latest\windows\include;D:\oneapi\compiler\latest\windows\compiler\include;D:\oneapi\compiler\latest\windows\compiler\include\intel64;D:\oneapi\tbb\latest\env\..\include;D:\oneapi\mkl\latest\include;D:\oneapi\ipp\latest\include;D:\oneapi\dpl\latest\windows\include;D:\oneapi\dpcpp-ct\latest\env\..\include;D:\oneapi\dev-utilities\latest\include;D:\oneapi\dal\latest\include;D:\oneapi\compiler\latest\windows\include;D:\oneapi\compiler\latest\windows\compiler\include;D:\oneapi\compiler\latest\windows\compiler\include\intel64;
CUDA_PATH = C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.3
CUDA_PATH_V11_3 = C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v11.3
CUSTOMWORKSPACE = 
DAALROOT = D:\oneapi\dal\latest
DALROOT = D:\oneapi\dal\latest
DAL_MAJOR_BINARY = 1
DAL_MINOR_BINARY = 1
DATESTAMP = 20250703_002209
DD = 03
DEVENVDIR = D:\VS2022\Common7\IDE\
DPL_ROOT = D:\oneapi\dpl\latest
DRIVERDATA = C:\Windows\System32\Drivers\DriverData
DSY_TENANT = OnPremise
DT = 20250703002209.914000+480

ERRORSTATE = 0
EXTENSIONSDKDIR = C:\Program Files (x86)\Microsoft SDKs\Windows Kits\10\ExtensionSDKs
EXTERNAL_INCLUDE = D:\VS2022\VC\Tools\MSVC\14.44.35207\include;D:\VS2022\VC\Tools\MSVC\14.44.35207\ATLMFC\include;D:\VS2022\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt
FED_DSFLEX_LICENSE_CONFIG = 27800@while
FED_LICENSE_SERVER_TYPE = DSFLEX
FOR_DIAGNOSTIC_LOG_FILE = fort.7
FOR_DUMP_CORE_FILE = TRUE
FOR_FORCE_STACK_TRACE = TRUE
FOR_IGNORE_EXCEPTIONS = TRUE
FRAMEWORK40VERSION = v4.0
FRAMEWORKDIR = C:\Windows\Microsoft.NET\Framework64\
FRAMEWORKDIR64 = C:\Windows\Microsoft.NET\Framework64\
FRAMEWORKVERSION = v4.0.30319
FRAMEWORKVERSION64 = v4.0.30319
HH = 00
HOMEDRIVE = C:
HOMEPATH = \Users\kz
IFORT_COMPILER22 = D:\oneapi\compiler\2022.2.0\windows\
INCLUDE = D:\oneapi\compiler\latest\windows\include;D:\oneapi\compiler\latest\windows\compiler\include;D:\oneapi\compiler\latest\windows\compiler\include\intel64;D:\oneapi\compiler\latest\windows\include;D:\oneapi\compiler\latest\windows\compiler\include;D:\oneapi\compiler\latest\windows\compiler\include\intel64;D:\oneapi\compiler\latest\windows\include;D:\oneapi\compiler\latest\windows\compiler\include;D:\oneapi\compiler\latest\windows\compiler\include\intel64;D:\oneapi\compiler\latest\windows\include;D:\oneapi\compiler\latest\windows\compiler\include;D:\oneapi\compiler\latest\windows\compiler\include\intel64;D:\oneapi\tbb\latest\env\..\include;D:\oneapi\mpi\latest\env\..\include;D:\oneapi\mkl\latest\include;D:\oneapi\itac\latest\include;D:\oneapi\ipp\latest\include;D:\oneapi\dpcpp-ct\latest\env\..\include;D:\oneapi\dev-utilities\latest\include;D:\oneapi\dal\latest\include;D:\oneapi\compiler\latest\windows\include;D:\oneapi\compiler\latest\windows\compiler\include;D:\oneapi\compiler\latest\windows\compiler\include\intel64;D:\VS2022\VC\Tools\MSVC\14.44.35207\include;D:\VS2022\VC\Tools\MSVC\14.44.35207\ATLMFC\include;D:\VS2022\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt
INSPECTOR_2022_DIR = D:\oneapi\inspector\latest\
INTELFPGAOCLSDKROOT = D:\oneapi\compiler\latest\windows\lib\oclfpga
INTELGTDEBUGGERROOT = D:\oneapi\debugger\latest\env\\..
INTEL_TARGET_ARCH = intel64
INTEL_TARGET_PLATFORM = windows
IPPROOT = D:\oneapi\ipp\latest
IPP_TARGET_ARCH = intel64
ITAC_ARCH = Intel(R) 64
I_MPI_ONEAPI_ROOT = D:\oneapi\mpi\2021.7.0
I_MPI_ROOT = D:\oneapi\mpi\latest\env\..
KMP_INIT_AT_FORK = FALSE
LIB = D:\oneapi\compiler\latest\windows\compiler\lib;D:\oneapi\compiler\latest\windows\compiler\lib\intel64_win;D:\oneapi\compiler\latest\windows\lib;D:\oneapi\compiler\latest\windows\lib\x64;D:\oneapi\compiler\latest\windows\compiler\lib;D:\oneapi\compiler\latest\windows\compiler\lib\intel64_win;D:\oneapi\compiler\latest\windows\lib;D:\oneapi\compiler\latest\windows\lib\x64;D:\oneapi\compiler\latest\windows\compiler\lib;D:\oneapi\compiler\latest\windows\compiler\lib\intel64_win;D:\oneapi\compiler\latest\windows\lib;D:\oneapi\compiler\latest\windows\lib\x64;D:\oneapi\compiler\latest\windows\compiler\lib;D:\oneapi\compiler\latest\windows\compiler\lib\intel64_win;D:\oneapi\compiler\latest\windows\lib;D:\oneapi\compiler\latest\windows\lib\x64;D:\oneapi\tbb\latest\env\..\lib\intel64\vc_mt;D:\oneapi\mpi\latest\env\..\lib\release;D:\oneapi\mpi\latest\env\..\lib;D:\oneapi\mkl\latest\lib\intel64;D:\oneapi\ipp\latest\lib\intel64;D:\oneapi\dal\latest\lib\intel64;D:\oneapi\compiler\latest\windows\compiler\lib;D:\oneapi\compiler\latest\windows\compiler\lib\intel64_win;D:\oneapi\compiler\latest\windows\lib;D:\oneapi\compiler\latest\windows\lib\x64;D:\VS2022\VC\Tools\MSVC\14.44.35207\ATLMFC\lib\x64;D:\VS2022\VC\Tools\MSVC\14.44.35207\lib\x64;C:\Program Files (x86)\Windows Kits\10\lib\10.0.26100.0\ucrt\x64;C:\Program Files (x86)\Windows Kits\10\\lib\10.0.26100.0\\um\x64
LIBPATH = D:\VS2022\VC\Tools\MSVC\14.44.35207\ATLMFC\lib\x64;D:\VS2022\VC\Tools\MSVC\14.44.35207\lib\x64;D:\VS2022\VC\Tools\MSVC\14.44.35207\lib\x86\store\references;C:\Program Files (x86)\Windows Kits\10\UnionMetadata\10.0.26100.0;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0;C:\Windows\Microsoft.NET\Framework64\v4.0.30319
LIBRARY_KIND = release
LIBRARY_PATH = D:\oneapi\ipp\latest\lib\intel64;
LOCALAPPDATA = C:\Users\<USER>\AppData\Local
LOGONSERVER = \\WHILE
MIN = 22
MKLROOT = D:\oneapi\mkl\latest
MKL_NUM_THREADS = 1
MM = 07
MP_NUMBER_OF_THREADS = 1
MSMPI_BENCHMARKS = C:\Program Files\Microsoft MPI\Benchmarks\
MSMPI_BIN = C:\Program Files\Microsoft MPI\Bin\
NLSPATH = D:\oneapi\mkl\latest\lib\intel64;
NOLICENSECHECK = true
NUMBER_OF_PROCESSORS = 8
NVCUDASAMPLES11_3_ROOT = C:\ProgramData\NVIDIA Corporation\CUDA Samples\v11.3
NVCUDASAMPLES_ROOT = C:\ProgramData\NVIDIA Corporation\CUDA Samples\v11.3
NVTOOLSEXT_PATH = C:\Program Files\NVIDIA Corporation\NvToolsExt\
OCL_ICD_FILENAMES = D:\oneapi\compiler\latest\windows\lib\x64\intelocl64_emu.dll;D:\oneapi\compiler\latest\windows\lib\x64\intelocl64.dll
OMP_NUM_THREADS = 1
ONEAPI_ROOT = D:\oneapi
ONEDRIVE = C:\Users\<USER>\OneDrive
OS = Windows_NT
PARALLEL_RESTARTFILE = SINGLE
PATH = C:\Users\<USER>\AppData\Local\Temp\kz_column_analysis_145028;D:\ABAQUS2024\product;D:\ABAQUS2024\product\win_b64;D:\ABAQUS2024\product\win_b64\code;D:\ABAQUS2024\product\win_b64\code\bin;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal;D:\ABAQUS2024\product\win_b64\CAEresources;D:\ABAQUS2024\product\win_b64\SMA;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal\Interop;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal\Elysium;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10\lib;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10\DLLs;D:\oneapi\compiler\latest\windows\bin;D:\oneapi\compiler\latest\windows\lib;D:\oneapi\compiler\latest\windows\bin\intel64;D:\oneapi\compiler\latest\windows\redist\intel64_win\compiler;D:\oneapi\compiler\latest\windows\lib\oclfpga\host\windows64\bin;D:\oneapi\compiler\latest\windows\lib\oclfpga\bin;;;;;;;;;;;;;;;;;;;D:\oneapi\tbb\latest\env\..\redist\intel64\vc_mt;D:\oneapi\mpi\latest\env\..\libfabric\bin\utils;D:\oneapi\mpi\latest\env\..\libfabric\bin;D:\oneapi\mpi\latest\env\..\bin\release;D:\oneapi\mpi\latest\env\..\bin;D:\oneapi\mkl\latest\redist\intel64;D:\oneapi\mkl\latest\bin\intel64;D:\oneapi\itac\latest\bin;D:\oneapi\itac\latest\dll;D:\oneapi\ipp\latest\redist\intel64;D:\oneapi\dpcpp-ct\latest\env\..\bin;D:\oneapi\dev-utilities\latest\bin;D:\oneapi\debugger\latest\env\\..\gdb\intel64\bin;D:\oneapi\dal\latest\redist\intel64;;;;;;;D:\VS2022\VC\Tools\MSVC\14.44.35207\bin\HostX64\x64;D:\VS2022\Common7\IDE\VC\VCPackages;D:\VS2022\Common7\IDE\CommonExtensions\Microsoft\TestWindow;D:\VS2022\Common7\IDE\CommonExtensions\Microsoft\TeamFoundation\Team Explorer;D:\VS2022\MSBuild\Current\bin\Roslyn;D:\VS2022\Team Tools\DiagnosticsHub\Collector;C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\\x64;C:\Program Files (x86)\Windows Kits\10\bin\\x64;D:\VS2022\\MSBuild\Current\Bin\amd64;C:\Windows\Microsoft.NET\Framework64\v4.0.30319;D:\VS2022\Common7\IDE\;D:\VS2022\Common7\Tools\;D:\Anaconda\condabin;D:\oneapi\mpi\latest\bin\;D:\oneapi\mpi\latest\bin\release\;D:\oneapi\mpi\latest\libfabric\bin\;D:\oneapi\mpi\latest\libfabric\bin\utils\;;D:\oneapi\compiler\latest\windows\redist\ia32_win\compiler;D:\ABAQUS2024\commands;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\bin32;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\bin;C:\mingw64\bin;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;D:\Python\Scripts\;D:\Python\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;;;D:\VS2022\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin;D:\VS2022\Common7\IDE\CommonExtensions\Microsoft\CMake\Ninja;D:\VS2022\Common7\IDE\VC\Linux\bin\ConnectionManagerExe;D:\VS2022\VC\vcpkg;D:\oneapi\compiler\latest\windows\lib\ocloc;D:\oneapi\inspector\latest\bin64;
PATHEXT = .COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC
PKG_CONFIG_PATH = D:\oneapi\compiler\latest\lib\pkgconfig;D:\oneapi\compiler\latest\lib\pkgconfig;D:\oneapi\compiler\latest\lib\pkgconfig;D:\oneapi\compiler\latest\lib\pkgconfig;D:\oneapi\tbb\latest\env\..\lib\pkgconfig;D:\oneapi\mpi\latest\env\..\lib\pkgconfig;D:\oneapi\mkl\latest\lib\pkgconfig;D:\oneapi\dpl\latest\lib\pkgconfig;D:\oneapi\dal\latest\lib\pkgconfig;D:\oneapi\compiler\latest\lib\pkgconfig;
PLATFORM = x64
POWERSHELL_DISTRIBUTION_CHANNEL = MSI:Windows 10 Pro
PROCESSOR_ARCHITECTURE = AMD64
PROCESSOR_IDENTIFIER = Intel64 Family 6 Model 60 Stepping 3, GenuineIntel
PROCESSOR_LEVEL = 6
PROCESSOR_REVISION = 3c03
PROGRAMDATA = C:\ProgramData
PROGRAMFILES = C:\Program Files
PROGRAMFILES(X86) = C:\Program Files (x86)
PROGRAMW6432 = C:\Program Files
PROMPT = $P$G
PSMODULEPATH = C:\Program Files\WindowsPowerShell\Modules;C:\Windows\system32\WindowsPowerShell\v1.0\Modules
PUBLIC = C:\Users\<USER>\ABAQUS2024\product\win_b64\code\python3.10\lib;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10\lib;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10\lib\site-packages;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10\DLLs;.;D:\ABAQUS2024\product;D:\ABAQUS2024\product\win_b64;D:\ABAQUS2024\product\win_b64\code;D:\ABAQUS2024\product\win_b64\code\bin;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal;D:\ABAQUS2024\product\win_b64\CAEresources;D:\ABAQUS2024\product\win_b64\SMA
PYTHONUNBUFFERED = nobuffering
PYVERDIRNAME = python3.10
RESULTS_DIR = column_analysis_20250703_002209
SCRIPT_NAME = vars.bat
SEC = 09
SESSIONNAME = Console
SETVARS_COMPLETED = 1
SMASVT_ROOT_PATH = D:\ABAQUS2024\product;D:\ABAQUS2024\product\win_b64;D:\ABAQUS2024\product\win_b64\code;D:\ABAQUS2024\product\win_b64\code\bin;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal;D:\ABAQUS2024\product\win_b64\CAEresources;D:\ABAQUS2024\product\win_b64\SMA
SYSTEMDRIVE = C:
SYSTEMROOT = C:\Windows
TARGET_VS = vs2022
TARGET_VS_ARCH = amd64
TBBROOT = D:\oneapi\tbb\latest\env\..
TBB_BIN_DIR = D:\oneapi\tbb\latest\env\
TBB_DLL_PATH = D:\oneapi\tbb\latest\env\..\redist\intel64\vc_mt
TBB_TARGET_ARCH = intel64
TBB_TARGET_VS = vc_mt
TEMP = C:\Users\<USER>\AppData\Local\Temp
TMP = C:\Users\<USER>\AppData\Local\Temp\kz_column_analysis_145028
TMPDIR = C:\Users\<USER>\AppData\Local\Temp\kz_column_analysis_145028
UCRTVERSION = 10.0.26100.0
UNIVERSALCRTSDKDIR = C:\Program Files (x86)\Windows Kits\10\
USERDOMAIN = WHILE
USERDOMAIN_ROAMINGPROFILE = WHILE
USERNAME = kz
USERPROFILE = C:\Users\<USER>\oneapi\compiler\2022.2.0\env\
VCIDEINSTALLDIR = D:\VS2022\Common7\IDE\VC\
VCINSTALLDIR = D:\VS2022\VC\
VCPKG_ROOT = D:\VS2022\VC\vcpkg
VCTOOLSINSTALLDIR = D:\VS2022\VC\Tools\MSVC\14.44.35207\
VCTOOLSREDISTDIR = D:\VS2022\VC\Redist\MSVC\14.44.35112\
VCTOOLSVERSION = 14.44.35207
VISUALSTUDIOVERSION = 17.0
VS170COMNTOOLS = D:\VS2022\Common7\Tools\
VS2022INSTALLDIR = D:\VS2022
VSCMD_ARG_APP_PLAT = Desktop
VSCMD_ARG_HOST_ARCH = x64
VSCMD_ARG_TGT_ARCH = x64
VSCMD_START_DIR = D:\oneapi
VSCMD_VER = 17.14.7
VSINSTALLDIR = D:\VS2022\
VT_ADD_LIBS = ws2_32.lib advapi32.lib
VT_DLL_DIR = D:\oneapi\itac\latest\dll
VT_FMPI_DLL = impi.dll
VT_LIB_DIR = D:\oneapi\itac\latest\dll
VT_MPI = impi64
VT_MPI_DLL = impi.dll
VT_ROOT = D:\oneapi\itac\latest
VT_SLIB_DIR = D:\oneapi\itac\latest\dll
WINDIR = C:\Windows
WINDOWSLIBPATH = C:\Program Files (x86)\Windows Kits\10\UnionMetadata\10.0.26100.0;C:\Program Files (x86)\Windows Kits\10\References\10.0.26100.0
WINDOWSSDKBINPATH = C:\Program Files (x86)\Windows Kits\10\bin\
WINDOWSSDKDIR = C:\Program Files (x86)\Windows Kits\10\
WINDOWSSDKLIBVERSION = 10.0.26100.0\
WINDOWSSDKVERBINPATH = C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\
WINDOWSSDKVERSION = 10.0.26100.0\
YY = 25
YYYY = 2025
_NT_SYMBOL_PATH = D:\ABAQUS2024\product;D:\ABAQUS2024\product\win_b64;D:\ABAQUS2024\product\win_b64\code;D:\ABAQUS2024\product\win_b64\code\bin;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal;D:\ABAQUS2024\product\win_b64\CAEresources;D:\ABAQUS2024\product\win_b64\SMA;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal\Interop;D:\ABAQUS2024\product\win_b64\code\bin\SMAExternal\Elysium;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10\lib;D:\ABAQUS2024\product\win_b64\tools\SMApy\python3.10\DLLs
__DOTNET_ADD_64BIT = 1
__DOTNET_PREFERRED_BITNESS = 64
__MS_VC_INSTALL_PATH = D:\VS2022\VC\Tools\MSVC\14.44.35207\
__VSCMD_PREINIT_PATH = D:\Anaconda\condabin;D:\oneapi\mpi\latest\bin\;D:\oneapi\mpi\latest\bin\release\;D:\oneapi\mpi\latest\libfabric\bin\;D:\oneapi\mpi\latest\libfabric\bin\utils\;D:\oneapi\compiler\latest\windows\redist\intel64_win\compiler;D:\oneapi\compiler\latest\windows\redist\ia32_win\compiler;D:\ABAQUS2024\commands;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\bin32;C:\Program Files (x86)\Common Files\Intel\Shared Libraries\bin;C:\mingw64\bin;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;D:\Python\Scripts\;D:\Python\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\mingw64\bin;
decfort_dump_flag = TRUE

# --------------- Execution Options -----------------
SIMExt = .sim
_xplSupportLibrary = ABQSMAShaShared-D
aba_jle_exclusions = {'adaptiveMesh': {'adaptiveMesh': ON}, 'inertiaReliefPerturbationLoadCases': {'inertiarelief': ON, 'perturbation': ON, 'loadcases': '5'}}
aba_jle_std_direct = 10,20
aba_jle_std_direct_memlimit = 8gb
aba_jle_std_direct_reqcpus = 32
aba_jle_std_iterative = 4
aba_jle_std_mbld = 6,12,24,48
aba_jle_xpl_all = 1
abaquslm_license_file = 27800@while
adaptiveMesh = OFF
ams = OFF
analysisType = EXPLICIT
applicationName = analysis
aqua = OFF
auto_calculate = ANALYSIS
auto_convert = ON
beamSectGen = OFF
biorid = OFF
cavityTypes = []
cavparallel = OFF
compile_cpp = ['cl', '/c', '/W0', '/MD', '/TP', '/EHsc', '/DNDEBUG', '/DWIN32', '/DTP_IP', '/D_CONSOLE', '/DNTI', '/DFLT_LIC', '/DOL_DOC', '/D__LIB__', '/DHKS_NT', '/D_WINDOWS_SOURCE', '/DFAR=', '/D_WINDOWS', '/DABQ_WIN86_64', '%P', '/I%I', '/ID:\\ABAQUS2024\\product']
compile_fmu = ['win64CmpWrp', '-m64', '-msvc9', 'cl', '/LD', '/D_WINDOWS', '/TC', '/W0', '/I%I', '/ID:\\ABAQUS2024\\product']
compile_fortran = ['ifort', '/c', '/fpp', '/extend-source', '/DABQ_WIN86_64', '/DABQ_FORTRAN', '/iface:cref', '/recursive', '/Qauto', '/align:array64byte', '/Qpc64', '/Qprec-div', '/Qprec-sqrt', '/Qfma-', '/fp:precise', '/Qimf-arch-consistency:true', '/Qfp-speculation:safe', '/Qprotect-parens', '/Qfp-stack-check', '/reentrancy:threaded', '/QxSSE3', '/QaxAVX', '/include:%I', '/include:D:\\ABAQUS2024\\product', '%P']
complexFrequency = OFF
connect_timeout = 30
contact = OFF
cosimulation = OFF
coupledProcedure = OFF
cpus = 1
cpusValidated = True
cse = OFF
cyclicSymmetryModel = OFF
directCyclic = OFF
direct_port = None
doc_root = http://help.3ds.com
domains = 1
double_precision = BOTH
dsa = OFF
dynStepSenseAdj = OFF
dynamic = OFF
earlyEnvProcessed = False
exciteBodies = []
externalField = OFF
externalFieldCSEAux = OFF
externalFieldExtList = ['.sim', '.SMAManifest']
externalFieldFiles = []
externalFieldSimReader = None
fieldImport = OFF
filPrt = []
fils = []
finitesliding = OFF
flexibleBody = OFF
foundation = OFF
freqSimReq = OFF
geostatic = OFF
heatTransfer = OFF
impJobExpVars = {}
importJobList = []
importSim = OFF
importer = OFF
importerParts = OFF
includes = []
indir = D:\column\c2\fortran_vumat\column_analysis_20250703_002209
inertiarelief = OFF
initialConditionsFile = OFF
input = concrete_column
inputFormat = INP
interactive = None
interpolExtList = ['.odb', '.sim', '.SMAManifest']
jleJobExcluded = False
job = column_analysis
keyword_licenses = []
lanczos = OFF
lastmessageonly = OFF
libs = []
license_server_type = FLEXNET
link_exe = ['LINK', '/nologo', '/INCREMENTAL:NO', '/subsystem:console', '/machine:AMD64', '/STACK:20000000', '/NODEFAULTLIB:LIBC.LIB', '/NODEFAULTLIB:LIBCMT.LIB', '/DEFAULTLIB:OLDNAMES.LIB', '/DEFAULTLIB:LIBIFCOREMD.LIB', '/DEFAULTLIB:LIBIFPORTMD.LIB', '/DEFAULTLIB:LIBMMD.LIB', '/DEFAULTLIB:kernel32.lib', '/DEFAULTLIB:user32.lib', '/DEFAULTLIB:advapi32.lib', '/FIXED:NO', '/LARGEADDRESSAWARE', '/out:%J', '%F', '%M', '%L', '%B', '%O', 'oldnames.lib', 'user32.lib', 'ws2_32.lib', 'netapi32.lib', 'advapi32.lib', 'msvcrt.lib', 'vcruntime.lib', 'ucrt.lib']
link_sl = ['LINK', '/nologo', '/NOENTRY', '/INCREMENTAL:NO', '/subsystem:console', '/machine:AMD64', '/NODEFAULTLIB:LIBC.LIB', '/NODEFAULTLIB:LIBCMT.LIB', '/DEFAULTLIB:OLDNAMES.LIB', '/DEFAULTLIB:LIBIFCOREMD.LIB', '/DEFAULTLIB:LIBIFPORTMD.LIB', '/DEFAULTLIB:LIBMMD.LIB', '/DEFAULTLIB:kernel32.lib', '/DEFAULTLIB:user32.lib', '/DEFAULTLIB:advapi32.lib', '/FIXED:NO', '/dll', '/def:%E', '/out:%U', '%F', '%A', '%L', '%B', 'oldnames.lib', 'user32.lib', 'ws2_32.lib', 'netapi32.lib', 'advapi32.lib', 'msvcrt.lib', 'vcruntime.lib', 'ucrt.lib']
listener_name = None
listener_resource = None
lmsuspend = None
magnetostatic = OFF
massDiffusion = OFF
materialresponse = OFF
message = OFF
messaging_mechanism = DIRECT
modifiedTet = OFF
moldflowFiles = []
moldflowMaterial = OFF
mp_environment_export = ('ABAQUSLM_LICENSE_FILE', 'ABAQUS_CCI_DEBUG', 'ABAQUS_CSE_CURRCONFIGMAPPING', 'ABAQUS_CSE_RELTIMETOLERANCE', 'ABAQUS_DEVL_MODE', 'ABAQUS_LANG', 'ABAQUS_MPF_DIAGNOSTIC_LEVEL', 'ABA_ADM_ALIGNMENT', 'ABA_ADM_MINIMUMDECREASE', 'ABA_ADM_MINIMUMINCREASE', 'ABA_ALL_ADB_IN_TMPDIR', 'ABA_CM_BUFFERING', 'ABA_CM_BUFFERING_LIMIT', 'ABA_CUTOFF_SLAVEFACET_ANGLE', 'ABA_DIRECT_SOLVER_PATH', 'ABA_DMPSOLVER_BWDPARALLELOFF', 'ABA_ELP_SURFACE_SPLIT', 'ABA_ELP_SUSPEND', 'ABA_ENABLE_DYNELEMS', 'ABA_EVOLVING_JOBS', 'ABA_EXT_SIMOUTPUT', 'ABA_GCONT_POOL_SIZE', 'ABA_HOME', 'ABA_INC_DEFAULT', 'ABA_ITERATIVE_SOLVER_VERBOSE', 'ABA_MEMORY_MODE', 'ABA_MPI_MESSAGE_TRACKING', 'ABA_MPI_VERBOSE_LEVEL', 'ABA_NEW_STOS_COHESIVE_APPROX_CONTACT', 'ABA_NEW_STOS_COHESIVE_CONTACT', 'ABA_NUM_INTEGRATION_POINTS_LINE3D', 'ABA_SHARED_SAVEDIR', 'ABA_PATH', 'ABA_PRE_DECOMPOSITION', 'ABA_PRINT_DYNELEMS', 'ABA_REMOVE_OVERCONSTRAINED_TIES', 'ABA_RESOURCE_MONITOR', 'ABA_RESOURCE_USEMALLINFO', 'ABA_RESULTS_OVERLAY', 'ABA_STD_ACTIVATE_CONTACT_FOR_AM', 'ABA_STD_MAX_SEARCH_DISTANCE', 'ABA_SYMBOLIC_GENERALCOLLAPSE', 'ABA_SYMBOLIC_GENERAL_MAXCLIQUERANK', 'ABA_TOSCA_JOB_STALL', 'ABA_TOSCA_OVERLAY', 'ABA_TOSCA_PROTOTYPE', 'ABA_TOSCA_SEQFILES', 'ABA_TOSCA_STALL', 'ABA_UNIT_INDEPENDENT_CONTACT', 'ABA_USE_NEW_STOS_SST_FORMULATION', 'ABA_USE_NEWTRACKDB', 'ABA_USE_OLD_SURF_TO_SURF_CONTACT', 'ABA_POTENTIAL_DEV', 'ABA_XPL_ALLMEMCHECKS', 'ABA_XPL_DEBUG', 'ABQLMHANGLIMIT', 'ABQLMIMPL', 'ABQLMQUEUE', 'ABQLMUSER', 'ABQ_ACTIVATE_PTK', 'ABQ_BYPASS_UNCONNECTED_REGION_CHECK', 'ABQ_CRTMALLOC', 'ABQ_DEBUG_MPIRCP', 'ABQ_DEBUG_MPIRSH', 'ABQ_DATACHECK', 'ABQ_DLALLOCATOR', 'ABQ_MPIRSH_USING_OMPI', 'ABQ_PATTERN', 'ABQ_PATTERN_VALUE', 'ABQ_RECOVER', 'ABQ_RESTART', 'ABQ_SKIP_ANALYTIC_SURF_GCONT', 'ABQ_SPLITFILE', 'ABQ_STD_ACCUM_CSLIP', 'ABQ_STD_ACTIVATE_BEAM_ROTATION', 'ABQ_STD_ALLOW_SURFACE_TO_BEAM', 'ABQ_SUPERELASTIC_MODIFIED', 'ABQ_GC_HEAT', 'ABQ_GC_SMALL', 'ABQ_UREG_USE_CONTACT_ELEM', 'ABQ_XFEM_POREPRESSURE', 'ABQ_XPL_DSMABORT', 'ABQ_XPL_PARTITIONSIZE', 'ABQ_XPL_WINDOWDUMP', 'ACML_FAST_MALLOC', 'ACML_FAST_MALLOC_CHUNK_SIZE', 'ACML_FAST_MALLOC_DEBUG', 'ACML_FAST_MALLOC_MAX_CHUNKS', 'ADB_USE_OLDSLDB', 'ADB_USE_NEWSLDB', 'CCI_RENDEZVOUS', 'DOMAIN', 'DOMAIN_CPUS', 'DOUBLE_PRECISION', 'DSLS_AUTH_PATHNAME', 'DSLS_CONFIG', 'FI_PROVIDER', 'FI_PROVIDER_PATH', 'FLEXLM_DIAGNOSTICS', 'FOR0006', 'FOR0064', 'FOR_DISABLE_DIAGNOSTIC_DISPLAY', 'FOR_IGNORE_EXCEPTIONS', 'I_MPI_FABRICS', 'I_MPI_OFI_PROVIDER', 'IPATH_NO_CPUAFFINITY', 'LD_PRELOAD', 'MALLOC_MMAP_THRESHOLD_', 'MKL_DYNAMIC', 'MKL_NUM_THREADS', 'MPCCI_CODEID', 'MPCCI_DEBUG', 'MPCCI_JOBID', 'MPCCI_NETDEVICE', 'MPCCI_SERVER', 'MPCCI_TINFO', 'MPC_GANG', 'MPIEXEC_AFFINITY_TABLE', 'MPI_FLAGS', 'MPI_FLUSH_FCACHE', 'MPI_PROPAGATE_TSTP', 'MPI_SOCKBUFSIZE', 'MPI_USE_MALLOPT_MMAP_MAX', 'MPI_USE_MALLOPT_MMAP_THRESHOLD', 'MPI_USE_MALLOPT_SBRK_PROTECTION', 'MPI_WORKDIR', 'MPIR_CVAR_CH4_OFI_TAG_BITS', 'MPIR_CVAR_CH4_OFI_RANK_BITS', 'MP_NUMBER_OF_THREADS', 'MPICH_ND_ZCOPY_THRESHOLD', 'NCPUS', 'OMP_DYNAMIC', 'OMP_NUM_THREADS', 'OUTDIR', 'PAIDUP', 'PARALLEL_METHOD', 'RAIDEV_NDREG_LAZYMEM', 'SMA_PARENT', 'SMA_PLATFORM', 'SMA_WS', 'SIMULIA_COSIN_PATH', 'STD_INITSTRESS_FLAG', 'STD_INITGEOICS_FLAG', 'UCX_IB_MLX5_DEVX', 'WEAR_ACCUM_ALGORITHM', 'XPL_HMP_COMMTHREAD')
mp_file_system = (DETECT, DETECT)
mp_mode = THREADS
mp_mpiCommand = []
mp_mpi_implementation = NATIVE
mp_mpi_searchpath = ['Microsoft MPI', 'Microsoft HPC Pack', 'Microsoft HPC Pack 2008 R2', 'Microsoft HPC Pack 2008', 'Microsoft HPC Pack 2008 SDK', 'Microsoft HPC Pack 2012']
mp_mpirun_path = C:\Program Files\Microsoft MPI\bin\mpiexec.exe
mp_rsh_command = dummy %H -l %U -n %C
multiphysics = OFF
noDmpDirect = []
noMultiHost = []
noMultiHostElemLoop = []
noStdParallel = []
no_domain_check = 1
numLoadCases = 0
onCaeGraphicsStartup = <function onCaeGraphicsStartup at 0x0000016D6A582B90>
onJobCompletion = []
onJobStartup = []
onestepinverse = OFF
outdir = D:\column\c2\fortran_vumat\column_analysis_20250703_002209
outputKeywords = ON
parallel_odb = SINGLE
parallel_restartfile = SINGLE
parameterized = OFF
partsAndAssemblies = OFF
parval = OFF
pgdHeatTransfer = OFF
plugin_central_dir = D:\ABAQUS2024\plugins
postOutput = OFF
preDecomposition = ON
queues = {}
randomResponse = OFF
restart = OFF
restartEndStep = OFF
restartIncrement = 0
restartStep = 0
restartWrite = OFF
resultsFormat = ODB
rezone = OFF
runCalculator = ON
simMode = SFS
simpackBodies = []
simwrk = D:\column\c2\fortran_vumat\column_analysis_20250703_002209\column_analysis
soils = OFF
soliter = OFF
solverTypes = ['DIRECT']
ssd = OFF
ssdCheckOK = False
standard_parallel = ALL
staticNonlinear = OFF
steadyStateTransport = OFF
step = ON
stepSenseAdj = OFF
stressExtList = ['.odb', '.sim', '.SMAManifest']
subGen = OFF
subGenResidual = OFF
submodel = OFF
substrLibDefs = OFF
substructure = OFF
symModGenModel = OFF
symmetricModelGeneration = OFF
symmetricResultsTransfer = OFF
tempNoInterpolExtList = ['.fil', '.odb', '.sim', '.SMAManifest']
thermal = OFF
tmpdir = C:\Users\<USER>\AppData\Local\Temp\kz_column_analysis_145028
tracer = OFF
transientSensitivity = OFF
umbrella_host = while
umbrella_port = 49386
unconnected_regions = OFF
unfold_param = OFF
unsymm = OFF
user = vumat_concrete.f
visco = OFF
xplSelect = OFF

