#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的VUMAT与PINN结果比较工具
只使用基本Python包进行比较
"""

import os
import sys

def read_basic_results(filename):
    """读取基础结果文件"""
    if not os.path.exists(filename):
        print(f"错误: 找不到文件 {filename}")
        return None, None
    
    times = []
    strains = []
    stresses = []
    
    try:
        with open(filename, 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()
    except UnicodeDecodeError:
        with open(filename, 'r', encoding='gbk') as f:
            lines = f.readlines()
    
    # 跳过头部，找到数据开始行
    data_start = False
    for line in lines:
        if 'Time,Strain,Stress' in line or '时间,应变,应力' in line:
            data_start = True
            continue
        
        if data_start and ',' in line:
            try:
                parts = line.strip().split(',')
                if len(parts) >= 3:
                    time = float(parts[0])
                    strain = float(parts[1])
                    stress = float(parts[2])
                    times.append(time)
                    strains.append(strain)
                    stresses.append(stress)
            except ValueError:
                continue
    
    return strains, stresses

def find_pinn_results():
    """查找最新的PINN预测结果"""
    base_dir = "../results"
    if not os.path.exists(base_dir):
        return None
    
    # 查找最新的session目录
    sessions = []
    for item in os.listdir(base_dir):
        if item.startswith('session_'):
            sessions.append(item)
    
    if not sessions:
        # 检查default目录
        default_pred = os.path.join(base_dir, "default", "prediction")
        if os.path.exists(default_pred):
            for file in os.listdir(default_pred):
                if file.endswith('.txt') and 'prediction' in file:
                    return os.path.join(default_pred, file)
        return None
    
    # 使用最新的session
    latest_session = sorted(sessions)[-1]
    pred_dir = os.path.join(base_dir, latest_session, "prediction")
    
    if os.path.exists(pred_dir):
        for file in os.listdir(pred_dir):
            if file.endswith('.txt') and 'prediction' in file:
                return os.path.join(pred_dir, file)
    
    return None

def read_pinn_results(filename):
    """读取PINN预测结果"""
    if not filename or not os.path.exists(filename):
        print(f"错误: 找不到PINN结果文件 {filename}")
        return None, None
    
    strains = []
    stresses = []
    
    try:
        with open(filename, 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()
    except UnicodeDecodeError:
        with open(filename, 'r', encoding='gbk') as f:
            lines = f.readlines()
    
    # 查找数据部分
    data_start = False
    for line in lines:
        line = line.strip()
        if 'strain' in line.lower() and 'stress' in line.lower():
            data_start = True
            continue
        
        if data_start and ',' in line:
            try:
                parts = line.split(',')
                if len(parts) >= 2:
                    strain = float(parts[0])
                    stress = float(parts[1])
                    strains.append(strain)
                    stresses.append(stress)
            except ValueError:
                continue
    
    return strains, stresses

def calculate_statistics(vumat_strains, vumat_stresses, pinn_strains, pinn_stresses):
    """计算统计比较结果"""
    print("\n=== 结果统计比较 ===")
    
    print(f"VUMAT数据点数: {len(vumat_strains)}")
    print(f"PINN数据点数: {len(pinn_strains)}")
    
    print(f"\nVUMAT应变范围: [{min(vumat_strains):.6f}, {max(vumat_strains):.6f}]")
    print(f"PINN应变范围: [{min(pinn_strains):.6f}, {max(pinn_strains):.6f}]")
    
    print(f"\nVUMAT应力范围: [{min(vumat_stresses):.3f}, {max(vumat_stresses):.3f}]")
    print(f"PINN应力范围: [{min(pinn_stresses):.3f}, {max(pinn_stresses):.3f}]")
    
    # 计算残余应变
    vumat_residual = vumat_strains[-1] if vumat_strains else 0
    pinn_residual = pinn_strains[-1] if pinn_strains else 0
    
    print(f"\nVUMAT最终应变: {vumat_residual:.6f}")
    print(f"PINN最终应变: {pinn_residual:.6f}")
    print(f"残余应变差异: {abs(vumat_residual - pinn_residual):.6f}")

def main():
    print("VUMAT与PINN结果简化比较工具")
    print("=" * 40)
    
    # 读取VUMAT结果
    vumat_file = "basic_results.txt"
    vumat_strains, vumat_stresses = read_basic_results(vumat_file)
    
    if vumat_strains is None:
        print("无法读取VUMAT结果，退出")
        return
    
    print(f"✓ 成功读取VUMAT结果: {len(vumat_strains)}个数据点")
    
    # 查找并读取PINN结果
    pinn_file = find_pinn_results()
    if pinn_file:
        print(f"✓ 找到PINN结果文件: {pinn_file}")
        pinn_strains, pinn_stresses = read_pinn_results(pinn_file)
        
        if pinn_strains:
            print(f"✓ 成功读取PINN结果: {len(pinn_strains)}个数据点")
            calculate_statistics(vumat_strains, vumat_stresses, pinn_strains, pinn_stresses)
        else:
            print("⚠ 无法解析PINN结果文件")
    else:
        print("⚠ 未找到PINN预测结果文件")
        print("请确保已运行PINN预测")
    
    # 生成简单的文本报告
    report_file = "comparison_report.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("VUMAT与PINN结果比较报告\n")
        f.write("=" * 30 + "\n\n")
        f.write(f"VUMAT数据点数: {len(vumat_strains)}\n")
        f.write(f"VUMAT应变范围: [{min(vumat_strains):.6f}, {max(vumat_strains):.6f}]\n")
        f.write(f"VUMAT应力范围: [{min(vumat_stresses):.3f}, {max(vumat_stresses):.3f}]\n")
        if pinn_file and pinn_strains:
            f.write(f"\nPINN数据点数: {len(pinn_strains)}\n")
            f.write(f"PINN应变范围: [{min(pinn_strains):.6f}, {max(pinn_strains):.6f}]\n")
            f.write(f"PINN应力范围: [{min(pinn_stresses):.3f}, {max(pinn_stresses):.3f}]\n")
    
    print(f"\n✓ 比较报告已保存到: {report_file}")

if __name__ == "__main__":
    main() 