@echo off
rem 混凝土柱VUMAT分析批处理脚本

echo ===================================================
echo    混凝土柱VUMAT分析
echo ===================================================

rem 检查Abaqus命令是否可用
where abaqus >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ 找到Abaqus命令
) else (
    echo ✗ 未找到Abaqus命令，请确保Abaqus已安装且在PATH中
    pause
    exit /b 1
)

echo.
echo 步骤1: 创建结果文件夹
echo -------------------
rem 创建带时间戳的结果文件夹
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YY=%dt:~2,2%" & set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "datestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

set "RESULTS_DIR=column_analysis_%datestamp%"
mkdir "%RESULTS_DIR%" 2>nul
if exist "%RESULTS_DIR%" (
    echo ✓ 创建结果文件夹: %RESULTS_DIR%
) else (
    echo ✗ 创建结果文件夹失败
    pause
    exit /b 1
)

rem 将输入文件复制到结果文件夹
copy "concrete_column.inp" "%RESULTS_DIR%\"
copy "vumat_concrete.f" "%RESULTS_DIR%\"
copy "material_parameters.inc" "%RESULTS_DIR%\" 2>nul
echo ✓ 输入文件已复制到结果文件夹

echo.
echo 步骤2: 切换到结果文件夹并运行Abaqus分析
echo -----------------------------------
cd "%RESULTS_DIR%"

rem 运行Abaqus分析
abaqus job=column_analysis input=concrete_column user=vumat_concrete.f double=both interactive
if %ERRORLEVEL% EQU 0 (
    echo ✓ Abaqus分析提交成功
) else (
    echo ✗ Abaqus分析提交失败
    cd ..
    pause
    exit /b 1
)

echo.
echo 分析正在运行中，请等待...
echo 结果文件将保存在: %RESULTS_DIR%
echo.
echo 您可以：
echo 1. 在Abaqus CAE中监控作业状态
echo 2. 查看 %RESULTS_DIR%\column_analysis.sta 文件了解分析进度
echo 3. 等待分析完成后运行结果提取脚本

rem 返回原目录
cd ..

rem 创建结果文件夹路径记录文件
echo %RESULTS_DIR% > latest_analysis_folder.txt
echo ✓ 最新分析文件夹路径已保存到 latest_analysis_folder.txt

echo.
echo ===================================================
echo    分析已提交！
echo ===================================================
echo.
echo 结果文件夹: %RESULTS_DIR%
echo 完成后该文件夹将包含：
echo   - column_analysis.odb  (结果数据库)
echo   - column_analysis.dat  (分析日志)
echo   - column_analysis.sta  (状态文件)
echo   - column_analysis.msg  (消息文件)
echo   - 其他Abaqus生成的文件
echo.
echo 分析完成后，请运行 run_column_extract.bat 提取结果。
echo.
echo 完成！
pause 