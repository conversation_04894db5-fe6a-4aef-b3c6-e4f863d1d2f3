@echo off
setlocal enabledelayedexpansion
rem 清理失败的分析并重新运行

echo ===================================================
echo    清理失败的分析并重新运行
echo ===================================================

echo.
echo 步骤1: 清理失败的分析文件夹
echo ------------------------

if exist "latest_analysis_folder.txt" (
    set /p OLD_FOLDER=<latest_analysis_folder.txt
    if exist "!OLD_FOLDER!" (
        echo 正在删除失败的分析文件夹: !OLD_FOLDER!
        rmdir /s /q "!OLD_FOLDER!"
        if exist "!OLD_FOLDER!" (
            echo ✗ 删除失败，请手动删除文件夹: !OLD_FOLDER!
        ) else (
            echo ✓ 成功删除失败的分析文件夹
        )
    )
    del "latest_analysis_folder.txt" 2>nul
)

echo.
echo 步骤2: 重新运行完整工作流程
echo ------------------------
echo 输入文件已修复，现在重新运行分析...
echo.

pause

call run_complete_workflow.bat

echo.
echo ===================================================
echo    完成！
echo =================================================== 