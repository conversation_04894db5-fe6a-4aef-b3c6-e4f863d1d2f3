@echo off
rem 混凝土柱结果提取和对比分析批处理脚本

echo ===================================================
echo    混凝土柱VUMAT结果提取与PINN对比
echo ===================================================

echo.
echo 步骤1: 查找最新的分析结果文件夹
echo -----------------------------
if exist "latest_analysis_folder.txt" (
    set /p RESULTS_FOLDER=<latest_analysis_folder.txt
    echo ✓ 找到分析结果文件夹: !RESULTS_FOLDER!
) else (
    echo ✗ 未找到分析结果记录，请先运行 run_column_analysis.bat
    pause
    exit /b 1
)

rem 检查结果文件夹是否存在
if not exist "!RESULTS_FOLDER!" (
    echo ✗ 分析结果文件夹不存在: !RESULTS_FOLDER!
    echo 请先运行 run_column_analysis.bat 完成分析
    pause
    exit /b 1
)

echo.
echo 步骤2: 检查分析是否完成
echo -------------------
if exist "!RESULTS_FOLDER!\column_analysis.odb" (
    echo ✓ 找到ODB文件，分析已完成
) else (
    echo ✗ 未找到ODB文件，分析可能仍在进行中
    echo 请等待分析完成后再运行此脚本
    pause
    exit /b 1
)

echo.
echo 步骤3: 运行结果提取脚本
echo -------------------

rem 检查Abaqus命令是否可用
where abaqus >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ 找到Abaqus命令，在Abaqus Python环境中运行提取脚本
    
    abaqus python extract_column_results.py
    if %ERRORLEVEL% EQU 0 (
        echo ✓ 数据提取成功完成
    ) else (
        echo ✗ 数据提取失败，尝试使用普通Python运行...
        python extract_column_results.py
    )
) else (
    echo ✗ 未找到Abaqus命令，尝试使用普通Python运行脚本...
    python extract_column_results.py
)

echo.
echo 步骤4: 检查生成的文件
echo -----------------
set "DATA_FILE=!RESULTS_FOLDER!\column_results.csv"
set "REPORT_FILE=!RESULTS_FOLDER!\analysis_report.txt"
set "PLOTS_DIR=!RESULTS_FOLDER!\plots"

if exist "!DATA_FILE!" (
    echo ✓ 数据文件: !DATA_FILE!
) else (
    echo ✗ 数据文件未生成
)

if exist "!REPORT_FILE!" (
    echo ✓ 分析报告: !REPORT_FILE!
) else (
    echo ✗ 分析报告未生成
)

if exist "!PLOTS_DIR!" (
    echo ✓ 图表目录: !PLOTS_DIR!
    echo   包含的图表文件：
    for %%f in ("!PLOTS_DIR!\*.png") do echo     - %%~nxf
) else (
    echo ✗ 图表目录未生成
)

echo.
echo ===================================================
echo    处理完成！
echo ===================================================
echo.
echo 结果文件夹: !RESULTS_FOLDER!
echo.
echo 生成的文件：
if exist "!DATA_FILE!" echo   - column_results.csv (原始数据)
if exist "!REPORT_FILE!" echo   - analysis_report.txt (分析报告)
if exist "!PLOTS_DIR!" echo   - plots/ (结果图表目录)
echo.
echo 重要图表文件：
if exist "!PLOTS_DIR!\stress_strain_comparison.png" echo   - plots\stress_strain_comparison.png (VUMAT与PINN滞回曲线对比)
if exist "!PLOTS_DIR!\displacement_time.png" echo   - plots\displacement_time.png (位移-时间曲线)
if exist "!PLOTS_DIR!\reaction_time.png" echo   - plots\reaction_time.png (反力-时间曲线)
if exist "!PLOTS_DIR!\reaction_displacement.png" echo   - plots\reaction_displacement.png (反力-位移滞回曲线)
echo.
echo 您可以打开这些图表文件查看分析结果！
echo.
echo 完成！
pause 