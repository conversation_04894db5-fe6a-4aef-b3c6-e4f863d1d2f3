@echo off
rem 编译和测试VUMAT的Windows批处理脚本

echo ===================================================
echo    混凝土损伤VUMAT编译和测试脚本 (Windows)
echo ===================================================

rem 检查Fortran编译器
where gfortran >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ 找到gfortran编译器
    set FORTRAN_COMPILER=gfortran
    goto :compiler_found
)

where ifort >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ 找到Intel Fortran编译器
    set FORTRAN_COMPILER=ifort
    goto :compiler_found
)

echo ✗ 未找到Fortran编译器，请安装gfortran或Intel Fortran
pause
exit /b 1

:compiler_found

echo.
echo 步骤1: 检查VUMAT语法
echo -------------------
%FORTRAN_COMPILER% -c vumat_concrete.f -o vumat_concrete.o
if %ERRORLEVEL% EQU 0 (
    echo ✓ VUMAT语法检查通过
    del vumat_concrete.o 2>nul
) else (
    echo ✗ VUMAT存在语法错误
    pause
    exit /b 1
)

echo.
echo 步骤2: 编译测试程序
echo ------------------
%FORTRAN_COMPILER% vumat_concrete.f test_vumat.f90 -o test_vumat.exe
if %ERRORLEVEL% EQU 0 (
    echo ✓ 测试程序编译成功
) else (
    echo ✗ 测试程序编译失败
    pause
    exit /b 1
)

echo.
echo 步骤3: 运行测试
echo ---------------
test_vumat.exe
if %ERRORLEVEL% EQU 0 (
    echo ✓ 测试运行完成
) else (
    echo ✗ 测试运行失败
    pause
    exit /b 1
)

echo.
echo 步骤4: 检查输出文件
echo -----------------
if exist "vumat_test_results.dat" (
    echo ✓ 生成了测试结果文件: vumat_test_results.dat
    for %%A in (vumat_test_results.dat) do echo 文件大小: %%~zA 字节
) else (
    echo ✗ 未生成测试结果文件
    pause
    exit /b 1
)

echo.
echo 步骤5: 生成快速分析图
echo --------------------
where python >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo import matplotlib.pyplot as plt > plot_results.py
    echo import numpy as np >> plot_results.py
    echo try: >> plot_results.py
    echo     data = np.loadtxt('vumat_test_results.dat', skiprows=1^) >> plot_results.py
    echo     strain = data[:, 1] >> plot_results.py
    echo     stress = data[:, 2] >> plot_results.py
    echo     plt.figure(figsize=(10, 6^)^) >> plot_results.py
    echo     plt.plot(strain, stress, 'b-', linewidth=2^) >> plot_results.py
    echo     plt.xlabel('Strain'^) >> plot_results.py
    echo     plt.ylabel('Stress (MPa^)'^) >> plot_results.py
    echo     plt.title('VUMAT Hysteresis Curve'^) >> plot_results.py
    echo     plt.grid(True, alpha=0.3^) >> plot_results.py
    echo     plt.savefig('vumat_verification.png', dpi=300, bbox_inches='tight'^) >> plot_results.py
    echo     print("✓ 生成验证图: vumat_verification.png"^) >> plot_results.py
    echo except Exception as e: >> plot_results.py
    echo     print(f"生成图形时出错: {e}"^) >> plot_results.py
    
    python plot_results.py
    del plot_results.py 2>nul
) else (
    echo 未找到Python，跳过图形生成
)

echo.
echo ===================================================
echo    VUMAT测试完成！
echo ===================================================
echo.
echo 生成的文件：
echo   - vumat_test_results.dat  (测试数据)
if exist "vumat_verification.png" echo   - vumat_verification.png  (验证图形)
echo.
echo 在Abaqus中使用：
echo   1. 将vumat_concrete.f编译为用户子程序
echo   2. 使用abaqus_input_example.inp作为参考
echo   3. 材料参数已在parameters_summary.txt中
echo.
echo 完成！
pause 