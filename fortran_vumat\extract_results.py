"""
提取Abaqus VUMAT混凝土损伤模型结果
此脚本从ODB文件中提取关键数据并生成可视化图表
"""
import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl

# 设置中文字体支持
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False    # 用来正常显示负号
except:
    print("警告: 无法设置中文字体，图表中的中文可能无法正确显示")

# 检查是否在Abaqus Python环境中运行
try:
    from odbAccess import *
    from abaqusConstants import *
    in_abaqus = True
    print("在Abaqus Python环境中运行")
except ImportError:
    in_abaqus = False
    print("警告: 未在Abaqus Python环境中运行，将尝试读取现有数据文件")

def extract_data_from_odb(odb_file='my_analysis.odb'):
    """从ODB文件中提取数据"""
    if not in_abaqus:
        print(f"错误: 无法直接读取ODB文件 {odb_file}，请在Abaqus Python环境中运行")
        return None
    
    print(f"正在打开ODB文件: {odb_file}")
    odb = openOdb(path=odb_file)
    
    # 获取最后一步
    step = odb.steps['CyclicLoading']
    
    # 提取历史输出数据
    region = step.historyRegions['Element PART-1-1.1 Int Point 1']
    
    # 获取应力、应变和状态变量数据
    stress_data = region.historyOutputs['S11'].data
    strain_data = region.historyOutputs['LE11'].data
    
    # 提取状态变量
    plastic_strain = region.historyOutputs['SDV1'].data
    tensile_damage = region.historyOutputs['SDV2'].data
    compressive_damage = region.historyOutputs['SDV3'].data
    r_max_plus = region.historyOutputs['SDV4'].data
    r_max_minus = region.historyOutputs['SDV5'].data
    
    # 关闭ODB文件
    odb.close()
    
    # 将数据整理为numpy数组
    time = np.array([t for t, _ in stress_data])
    stress = np.array([s for _, s in stress_data])
    strain = np.array([e for _, e in strain_data])
    plastic = np.array([p for _, p in plastic_strain])
    d_plus = np.array([d for _, d in tensile_damage])
    d_minus = np.array([d for _, d in compressive_damage])
    r_plus = np.array([r for _, r in r_max_plus])
    r_minus = np.array([r for _, r in r_max_minus])
    
    # 保存数据到文本文件
    data = np.column_stack((time, strain, stress, plastic, d_plus, d_minus, r_plus, r_minus))
    header = "Time,Strain,Stress,PlasticStrain,TensileDamage,CompressiveDamage,R_plus,R_minus"
    np.savetxt('vumat_results.csv', data, delimiter=',', header=header)
    
    print(f"数据已保存到 vumat_results.csv")
    return data

def read_data_from_file(filename='vumat_results.csv'):
    """从CSV文件读取数据"""
    try:
        data = np.loadtxt(filename, delimiter=',', skiprows=1)
        print(f"从文件 {filename} 读取数据成功")
        return data
    except Exception as e:
        print(f"读取文件 {filename} 失败: {e}")
        return None

def generate_plots(data):
    """生成结果图表"""
    if data is None:
        print("错误: 没有数据可供绘图")
        return
    
    # 解包数据
    time = data[:, 0]
    strain = data[:, 1]
    stress = data[:, 2]
    plastic_strain = data[:, 3]
    d_plus = data[:, 4]
    d_minus = data[:, 5]
    r_plus = data[:, 6]
    r_minus = data[:, 7]
    
    # 创建图表目录
    if not os.path.exists('results_plots'):
        os.makedirs('results_plots')
    
    # 1. 应力-应变滞回曲线
    plt.figure(figsize=(10, 8))
    plt.plot(strain, stress, 'b-', linewidth=2)
    plt.xlabel('应变', fontsize=14)
    plt.ylabel('应力 (MPa)', fontsize=14)
    plt.title('混凝土VUMAT模型应力-应变滞回曲线', fontsize=16)
    plt.grid(True, alpha=0.3)
    plt.savefig('results_plots/stress_strain_hysteresis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 2. 应变-时间曲线
    plt.figure(figsize=(10, 6))
    plt.plot(time, strain, 'r-', linewidth=2)
    plt.xlabel('时间 (s)', fontsize=14)
    plt.ylabel('应变', fontsize=14)
    plt.title('应变-时间曲线', fontsize=16)
    plt.grid(True, alpha=0.3)
    plt.savefig('results_plots/strain_time.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 3. 塑性应变演化
    plt.figure(figsize=(10, 6))
    plt.plot(time, plastic_strain, 'g-', linewidth=2)
    plt.xlabel('时间 (s)', fontsize=14)
    plt.ylabel('塑性应变', fontsize=14)
    plt.title('塑性应变演化曲线', fontsize=16)
    plt.grid(True, alpha=0.3)
    plt.savefig('results_plots/plastic_strain_evolution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 4. 损伤演化
    plt.figure(figsize=(10, 6))
    plt.plot(time, d_plus, 'b-', linewidth=2, label='拉伸损伤 d+')
    plt.plot(time, d_minus, 'r-', linewidth=2, label='压缩损伤 d-')
    plt.xlabel('时间 (s)', fontsize=14)
    plt.ylabel('损伤参数', fontsize=14)
    plt.title('损伤演化曲线', fontsize=16)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.savefig('results_plots/damage_evolution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 5. 损伤驱动力演化
    plt.figure(figsize=(10, 6))
    plt.plot(time, r_plus, 'b-', linewidth=2, label='拉伸损伤驱动力 r+')
    plt.plot(time, r_minus, 'r-', linewidth=2, label='压缩损伤驱动力 r-')
    plt.xlabel('时间 (s)', fontsize=14)
    plt.ylabel('损伤驱动力 (MPa)', fontsize=14)
    plt.title('损伤驱动力演化曲线', fontsize=16)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.savefig('results_plots/damage_driving_forces.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 6. 综合分析图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 应力-应变滞回曲线
    axes[0, 0].plot(strain, stress, 'b-', linewidth=2)
    axes[0, 0].set_xlabel('应变', fontsize=12)
    axes[0, 0].set_ylabel('应力 (MPa)', fontsize=12)
    axes[0, 0].set_title('应力-应变滞回曲线', fontsize=14)
    axes[0, 0].grid(True, alpha=0.3)
    
    # 塑性应变演化
    axes[0, 1].plot(time, plastic_strain, 'g-', linewidth=2)
    axes[0, 1].set_xlabel('时间 (s)', fontsize=12)
    axes[0, 1].set_ylabel('塑性应变', fontsize=12)
    axes[0, 1].set_title('塑性应变演化', fontsize=14)
    axes[0, 1].grid(True, alpha=0.3)
    
    # 损伤演化
    axes[1, 0].plot(time, d_plus, 'b-', linewidth=2, label='拉伸损伤 d+')
    axes[1, 0].plot(time, d_minus, 'r-', linewidth=2, label='压缩损伤 d-')
    axes[1, 0].set_xlabel('时间 (s)', fontsize=12)
    axes[1, 0].set_ylabel('损伤参数', fontsize=12)
    axes[1, 0].set_title('损伤演化', fontsize=14)
    axes[1, 0].legend(fontsize=10)
    axes[1, 0].grid(True, alpha=0.3)
    
    # 损伤驱动力
    axes[1, 1].plot(time, r_plus, 'b-', linewidth=2, label='r+')
    axes[1, 1].plot(time, r_minus, 'r-', linewidth=2, label='r-')
    axes[1, 1].set_xlabel('时间 (s)', fontsize=12)
    axes[1, 1].set_ylabel('损伤驱动力 (MPa)', fontsize=12)
    axes[1, 1].set_title('损伤驱动力演化', fontsize=14)
    axes[1, 1].legend(fontsize=10)
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('results_plots/comprehensive_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"所有图表已保存到 results_plots 目录")

def generate_report(data):
    """生成结果报告"""
    if data is None:
        print("错误: 没有数据可供生成报告")
        return
    
    # 解包数据
    time = data[:, 0]
    strain = data[:, 1]
    stress = data[:, 2]
    plastic_strain = data[:, 3]
    d_plus = data[:, 4]
    d_minus = data[:, 5]
    r_plus = data[:, 6]
    r_minus = data[:, 7]
    
    # 计算关键指标
    max_stress = np.max(stress)
    min_stress = np.min(stress)
    max_strain = np.max(strain)
    min_strain = np.min(strain)
    final_plastic_strain = plastic_strain[-1]
    max_tensile_damage = np.max(d_plus)
    max_compressive_damage = np.max(d_minus)
    
    # 生成报告
    report = f"""
混凝土VUMAT模型分析结果报告
=========================

分析时间: {time[-1]:.2f} 秒
总增量数: {len(time)}

关键指标:
--------
最大应力: {max_stress:.4f} MPa
最小应力: {min_stress:.4f} MPa
最大应变: {max_strain:.6f}
最小应变: {min_strain:.6f}
最终塑性应变: {final_plastic_strain:.6f}
最大拉伸损伤: {max_tensile_damage:.4f}
最大压缩损伤: {max_compressive_damage:.4f}

材料响应分析:
-----------
1. 滞回曲线特性:
   - 滞回环面积: 反映了材料的能量耗散能力
   - 残余应变: {final_plastic_strain:.6f}，表明材料经历了不可恢复的塑性变形

2. 损伤演化:
   - 拉伸损伤最大值: {max_tensile_damage:.4f}
   - 压缩损伤最大值: {max_compressive_damage:.4f}
   - 损伤演化速率可从损伤-时间曲线斜率观察

3. 塑性应变累积:
   - 塑性应变随循环加载逐渐累积
   - 最终塑性应变: {final_plastic_strain:.6f}

结论:
----
VUMAT模型成功模拟了混凝土材料在循环荷载下的非线性响应，包括:
- 应力-应变滞回行为
- 损伤演化过程
- 塑性应变累积
- 刚度退化

图表文件位于 results_plots 目录
"""
    
    # 保存报告
    with open('vumat_analysis_report.txt', 'w') as f:
        f.write(report)
    
    print(f"分析报告已保存到 vumat_analysis_report.txt")
    return report

def main():
    """主函数"""
    print("混凝土VUMAT模型结果提取与分析")
    print("==============================")
    
    # 尝试从ODB文件提取数据
    if in_abaqus:
        data = extract_data_from_odb()
    else:
        # 如果不在Abaqus环境中，尝试读取已有的CSV文件
        data = read_data_from_file()
        
        # 如果没有找到CSV文件，生成模拟数据用于测试
        if data is None:
            print("生成模拟数据用于测试...")
            # 创建模拟数据
            time = np.linspace(0, 1, 100)
            strain = 0.0006 * np.sin(2 * np.pi * time * 4.5)
            stress = 10000 * strain * (1 - 0.2 * np.cumsum(np.abs(np.diff(np.concatenate(([0], strain))))))
            plastic_strain = np.cumsum(0.1 * np.abs(np.diff(np.concatenate(([0], strain)))))
            d_plus = np.clip(0.8 * np.cumsum(np.maximum(0, np.diff(np.concatenate(([0], strain))))), 0, 0.8)
            d_minus = np.clip(0.6 * np.cumsum(np.maximum(0, -np.diff(np.concatenate(([0], strain))))), 0, 0.6)
            r_plus = 3.67043 + 2 * d_plus
            r_minus = 10.0 + 5 * d_minus
            
            data = np.column_stack((time, strain, stress, plastic_strain, d_plus, d_minus, r_plus, r_minus))
            np.savetxt('vumat_results_simulated.csv', data, delimiter=',', 
                      header="Time,Strain,Stress,PlasticStrain,TensileDamage,CompressiveDamage,R_plus,R_minus")
            print("模拟数据已保存到 vumat_results_simulated.csv")
    
    if data is not None:
        generate_plots(data)
        report = generate_report(data)
        print("\n报告摘要:")
        print("--------")
        print("\n".join(report.split("\n")[1:10]))  # 打印报告的前几行
    else:
        print("错误: 无法获取数据，无法生成图表和报告")

if __name__ == "__main__":
    main() 