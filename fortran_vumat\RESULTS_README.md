# 混凝土VUMAT模型结果提取与分析

本目录包含用于提取和分析Abaqus VUMAT混凝土损伤模型结果的脚本。

## 文件说明

- `extract_basic_data.py` - 基本数据提取脚本，只依赖Abaqus Python API
- `extract_results.py` - 完整结果提取和可视化脚本，需要NumPy和Matplotlib
- `run_basic_extract.bat` - 运行基本数据提取的批处理脚本
- `run_extract_results.bat` - 运行完整结果提取和可视化的批处理脚本

## 使用方法

### 基本数据提取

如果您只需要提取基本数据而不需要可视化，请使用：

```
run_basic_extract.bat
```

这将生成一个`basic_results.txt`文件，包含以下内容：
- 模型基本信息
- 应力-应变数据
- 关键结果指标（最大/最小应力、应变）
- 状态变量最终值

### 完整结果提取与可视化

如果您需要完整的结果提取和可视化，请使用：

```
run_extract_results.bat
```

这将生成以下文件：
- `vumat_results.csv` - 原始数据
- `vumat_analysis_report.txt` - 详细分析报告
- `results_plots/` 目录 - 包含以下图表：
  - 应力-应变滞回曲线
  - 应变-时间曲线
  - 塑性应变演化曲线
  - 损伤演化曲线
  - 损伤驱动力演化曲线
  - 综合分析图

## 需求

- 基本数据提取：只需要Abaqus Python环境
- 完整结果提取与可视化：需要Abaqus Python环境、NumPy和Matplotlib

## 注意事项

1. 这些脚本需要在Abaqus Python环境中运行才能直接读取ODB文件
2. 如果没有Abaqus环境，`run_extract_results.bat`会尝试使用普通Python运行，但只能读取已有的CSV数据文件
3. 如果没有可用的数据文件，脚本会生成模拟数据用于测试

## 结果解释

### 状态变量

VUMAT中定义的状态变量如下：
- SDV1: 累积塑性应变
- SDV2: 拉伸损伤参数 d+
- SDV3: 压缩损伤参数 d-
- SDV4: 最大拉伸损伤驱动力 r_max+
- SDV5: 最大压缩损伤驱动力 r_max- 