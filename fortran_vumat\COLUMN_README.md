# 混凝土柱VUMAT验证

本目录包含用于验证混凝土损伤模型VUMAT实现的文件和脚本。通过将PINN训练得到的参数应用于Abaqus显式动力学分析，验证模型在实际结构中的表现。

## 模型说明

- **几何形状**: 圆柱体，直径400mm，长度800mm
- **边界条件**: 底端固定，顶端施加循环位移荷载
- **材料模型**: 弹塑性损伤本构模型，使用VUMAT实现
- **参数来源**: PINN模型训练得到的参数

## 文件说明

- `concrete_column.inp` - Abaqus输入文件，定义混凝土柱模型
- `vumat_concrete.f` - 混凝土损伤VUMAT子程序
- `run_column_analysis.bat` - 运行Abaqus分析的批处理脚本
- `extract_column_results.py` - 结果提取和对比分析脚本
- `run_column_extract.bat` - 运行结果提取的批处理脚本

## 使用方法

### 选项1: 完整自动化工作流程（推荐）

执行以下命令进行完整的验证工作流程：

```
run_complete_workflow.bat
```

这将自动完成以下步骤：
1. 运行Abaqus分析
2. 监控分析进度
3. 分析完成后自动提取结果
4. 生成对比图表和报告

### 选项2: 分步执行

#### 步骤1: 运行Abaqus分析

```
run_column_analysis.bat
```

这将：
- 创建带时间戳的结果文件夹（如：`column_analysis_20250101_143022`）
- 复制输入文件到结果文件夹
- 在结果文件夹中运行Abaqus分析
- 记录结果文件夹路径到`latest_analysis_folder.txt`

#### 步骤2: 提取结果并对比

分析完成后，执行以下命令：

```
run_column_extract.bat
```

这将在结果文件夹中生成：
- `column_results.csv` - 提取的原始数据
- `analysis_report.txt` - 详细分析报告
- `plots/` 目录 - 包含以下图表：
  - `stress_strain_comparison.png` - VUMAT与PINN预测的应力-应变滞回曲线对比
  - `displacement_time.png` - 位移-时间曲线
  - `reaction_time.png` - 反力-时间曲线
  - `reaction_displacement.png` - 反力-位移滞回曲线

## 文件组织结构

```
c2/fortran_vumat/
├── concrete_column.inp           # Abaqus输入文件
├── vumat_concrete.f              # VUMAT子程序
├── run_complete_workflow.bat     # 完整工作流程脚本
├── run_column_analysis.bat       # 分析运行脚本
├── run_column_extract.bat        # 结果提取脚本
├── latest_analysis_folder.txt    # 最新分析文件夹记录
└── column_analysis_YYYYMMDD_HHMMSS/  # 结果文件夹
    ├── concrete_column.inp       # 输入文件副本
    ├── vumat_concrete.f          # VUMAT副本
    ├── column_analysis.odb       # Abaqus结果数据库
    ├── column_analysis.dat       # 分析日志
    ├── column_analysis.sta       # 状态文件
    ├── column_results.csv        # 提取的数据
    ├── analysis_report.txt       # 分析报告
    └── plots/                    # 图表目录
        ├── stress_strain_comparison.png
        ├── displacement_time.png
        ├── reaction_time.png
        └── reaction_displacement.png
```

## 验证内容

1. **滞回曲线形态**: 验证VUMAT实现的滞回曲线与PINN预测结果的一致性
2. **损伤演化**: 验证损伤参数随循环荷载的演化过程
3. **塑性应变累积**: 验证塑性应变的累积效应
4. **刚度退化**: 验证材料刚度随损伤的退化特性

## 参数说明

VUMAT中使用的材料参数如下：
- E0 = 10000.0 MPa (初始弹性模量)
- f_t = 3.67043 MPa (拉伸强度)
- f_c = 10.0 MPa (压缩强度)
- A+ = 0.84463 (拉伸损伤参数)
- B+ = 1.81372 (拉伸损伤参数)
- ξ+ = 0.5 (拉伸塑性参数)
- A- = 2.0 (压缩损伤参数)
- B- = 1.32925 (压缩损伤参数)
- ξ- = 0.50028 (压缩塑性参数)

## 注意事项

1. 运行分析需要Abaqus及其Python环境
2. 确保VUMAT子程序中的参数与PINN训练得到的参数一致
3. 结果提取脚本会自动尝试查找PINN预测结果进行对比
4. 如果没有找到PINN预测数据，脚本会生成模拟数据用于演示 