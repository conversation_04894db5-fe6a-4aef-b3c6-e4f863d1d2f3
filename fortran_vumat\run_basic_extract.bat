@echo off
rem 在Abaqus Python环境中运行基本数据提取脚本

echo ===================================================
echo    混凝土VUMAT模型基本数据提取
echo ===================================================

rem 检查Abaqus命令是否可用
where abaqus >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✓ 找到Abaqus命令
) else (
    echo ✗ 未找到Abaqus命令，请确保Abaqus已安装且在PATH中
    pause
    exit /b 1
)

echo.
echo 步骤1: 在Abaqus Python环境中运行基本数据提取脚本
echo -----------------------------------
abaqus python extract_basic_data.py
if %ERRORLEVEL% EQU 0 (
    echo ✓ 数据提取成功完成
) else (
    echo ✗ 数据提取失败
    pause
    exit /b 1
)

echo.
echo ===================================================
echo    处理完成！
echo ===================================================
echo.
echo 生成的文件：
if exist "basic_results.txt" echo   - basic_results.txt (基本分析结果)
echo.
echo 完成！
pause 